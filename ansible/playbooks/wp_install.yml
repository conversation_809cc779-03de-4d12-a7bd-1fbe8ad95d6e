---
- name: Deploy a New WordPress Site
  hosts: all
  gather_facts: false
  remote_user: '{{ user }}'
  # no_log: true
  vars:
    ansible_user: '{{ user }}'
    ansible_port: '{{ ssh_port }}'
  vars_files:
    - ../configs/user.yml
    - ../configs/mysql.yml

  roles:
    - wp_install
    - wp_nginx
    - wp_post_install

  post_tasks:
    - name: Wait for WordPress to become available
      ansible.builtin.pause:
        seconds: 15

    - name: Verify WordPress is accessible
      ansible.builtin.uri:
        url: 'https://{{ domain_name }}'
        method: GET
        status_code: [200, 301, 302]
        timeout: 30
        validate_certs: true
      register: wp_response
      until: wp_response.status in [200, 301, 302]
      retries: 5
      delay: 5
      # delegate_to: localhost
