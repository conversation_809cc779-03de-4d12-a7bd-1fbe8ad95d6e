---
- name: Set fact for site_root_dir
  ansible.builtin.set_fact:
    site_root_dir: '/home/<USER>/sites/{{ domain_name }}'

- name: Set WordPress site tagline
  ansible.builtin.command:
    cmd: /usr/local/bin/wp option update blogdescription "{{ wp_site_tagline }}" --path={{ site_root_dir }}/public
  changed_when: true

- name: Get currently active theme
  ansible.builtin.command:
    cmd: /usr/local/bin/wp theme list --status=active --field=name --path={{ site_root_dir }}/public
  register: active_theme
  changed_when: false

- name: Get list of all themes
  ansible.builtin.command:
    cmd: /usr/local/bin/wp theme list --field=name --path={{ site_root_dir }}/public
  register: all_themes
  changed_when: false

- name: Remove all themes except active
  ansible.builtin.command:
    cmd: /usr/local/bin/wp theme delete {{ item }} --path={{ site_root_dir }}/public
  loop: '{{ all_themes.stdout_lines | difference(active_theme.stdout_lines | default([])) }}'
  when: all_themes.stdout_lines | default([]) | length > 0
  failed_when: false
  changed_when: true

- name: Get list of all plugins
  ansible.builtin.command:
    cmd: /usr/local/bin/wp plugin list --field=name --path={{ site_root_dir }}/public
  register: all_plugins
  changed_when: false

- name: Remove all plugins
  ansible.builtin.command:
    cmd: /usr/local/bin/wp plugin delete {{ item }} --path={{ site_root_dir }}/public
  loop: '{{ all_plugins.stdout_lines | default([]) }}'
  when: all_plugins.stdout_lines | default([]) | length > 0
  failed_when: false
  changed_when: true
