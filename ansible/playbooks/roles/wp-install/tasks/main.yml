---
- name: Set root dir fact for {{ domain_name }}
  ansible.builtin.set_fact:
    site_root_dir: '/home/<USER>/sites/{{ domain_name }}'

- name: Check if site root directory exists
  ansible.builtin.stat:
    path: '{{ site_root_dir }}'
  register: site_root_exists

- name: Setting up root dir for {{ domain_name }}
  when: not site_root_exists.stat.exists
  ansible.builtin.file:
    path: '{{ item }}'
    state: directory
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0755'
  loop:
    - '{{ site_root_dir }}'
    - '{{ site_root_dir }}/cache'
    - '{{ site_root_dir }}/logs'
    - '{{ site_root_dir }}/public'
    - '{{ site_root_dir }}/backups'

- name: Create DB for {{ domain_name }}
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
  become: true

- name: Create DB user for {{ domain_name }}
  community.mysql.mysql_user:
    name: '{{ wp_db_user }}'
    password: '{{ wp_db_password }}'
    priv: '{{ wp_db_name }}.*:ALL'
    host: localhost
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Check WordPress installation status
  ansible.builtin.stat:
    path: '{{ site_root_dir }}/public/wp-config.php'
  register: wp_config_exists

- name: Download WordPress core
  ansible.builtin.command:
    cmd: /usr/local/bin/wp core download --version={{ wp_version }} --path={{ site_root_dir }}/public
    creates: '{{ site_root_dir }}/public/wp-load.php'
  when: not wp_config_exists.stat.exists

- name: Generate WordPress configuration
  ansible.builtin.command:
    cmd: >
      /usr/local/bin/wp config create
      --dbname={{ wp_db_name }}
      --dbuser={{ wp_db_user }}
      --dbpass={{ wp_db_password }}
      --dbhost=localhost
      --dbcharset=utf8mb4
      --dbcollate=utf8mb4_unicode_ci
      --path={{ site_root_dir }}/public
    creates: '{{ site_root_dir }}/public/wp-config.php'
  when: not wp_config_exists.stat.exists

- name: Set WordPress security salts
  ansible.builtin.command:
    cmd: /usr/local/bin/wp config shuffle-salts --path={{ site_root_dir }}/public
  when: not wp_config_exists.stat.exists
  register: shuffle_salts
  changed_when: shuffle_salts.rc == 0

- name: Check WordPress installation
  ansible.builtin.command:
    cmd: /usr/local/bin/wp core is-installed --path={{ site_root_dir }}/public
  register: wp_is_installed
  failed_when: wp_is_installed.rc not in [0, 1]
  changed_when: false

- name: Install WordPress
  ansible.builtin.command:
    cmd: >
      /usr/local/bin/wp core install
      --url=https://{{ domain_name }}
      --title="{{ wp_site_title }}"
      --admin_user={{ wp_admin_user }}
      --admin_password={{ wp_admin_password }}
      --admin_email={{ wp_admin_email }}
      --path={{ site_root_dir }}/public
      --skip-email
  when: wp_is_installed.rc != 0
  changed_when: wp_is_installed.rc != 0

- name: Remove default WP_DEBUG block from wp-config.php (if it exists)
  ansible.builtin.replace:
    path: '{{ site_root_dir }}/public/wp-config.php'
    regexp: "^if \\( ! defined\\( 'WP_DEBUG' \\) \\) \\{.*?^\\s*define\\( 'WP_DEBUG', (?:true|false) \\);.*?^\\}(?:\\s*\\n)?$"
    replace: ''
  when: wp_is_installed.rc != 0
  changed_when: wp_is_installed.rc != 0

# - name: Configure WordPress security settings
#   ansible.builtin.command:
#     cmd: /usr/local/bin/wp config set {{ item.key }} {{ item.value }} --path={{ site_root_dir }}/public
#   loop:
#     - { key: 'WP_POST_REVISIONS', value: 5 }
#     - { key: 'AUTOSAVE_INTERVAL', value: 300 }
#     - { key: 'DISALLOW_FILE_EDIT', value: true }
#     - { key: 'FORCE_SSL_ADMIN', value: true }
#     - { key: 'IMAGE_EDIT_OVERWRITE', value: true }
#     - { key: 'WP_AUTO_UPDATE_CORE', value: 'minor' }
#     - { key: 'WP_CACHE', value: true }
#     - { key: 'WP_CRON_LOCK_TIMEOUT', value: 60 }
#     - { key: 'WP_DEBUG_DISPLAY', value: false }
#     - { key: 'WP_DEBUG_LOG', value: true }
#     - { key: 'WP_DEBUG', value: true }
#     - { key: 'WP_DISABLE_FATAL_ERROR_HANDLER', value: false }
#   when: wp_is_installed.rc != 0
#   changed_when: wp_is_installed.rc != 0

- name: Add custom PHP and WordPress settings to wp-config.php
  ansible.builtin.blockinfile:
    path: '{{ site_root_dir }}/public/wp-config.php'
    block: |
      /* Custom PHP Error Logging Configuration */
      @ini_set( 'log_errors', 'On' );
      @ini_set( 'display_errors', 'Off' );
      @ini_set( 'error_log', '{{ site_root_dir }}/logs/php_error.log' );
      /* End Custom PHP Error Logging Configuration */

      /* Custom WordPress Configuration Settings */
      define( 'WP_POST_REVISIONS', 5 );
      define( 'AUTOSAVE_INTERVAL', 300 );
      define( 'DISALLOW_FILE_EDIT', true );
      define( 'FORCE_SSL_ADMIN', true );
      define( 'IMAGE_EDIT_OVERWRITE', true );
      define( 'WP_AUTO_UPDATE_CORE', 'minor' );
      define( 'WP_CACHE', true );
      define( 'WP_CRON_LOCK_TIMEOUT', 60 );
      define( 'WP_DISABLE_FATAL_ERROR_HANDLER', false );
      define( 'WP_DEBUG', true );
      define( 'WP_DEBUG_DISPLAY', false );
      define( 'WP_DEBUG_LOG', '{{ site_root_dir }}/logs/debug.log' );
      /* End Custom WordPress Configuration Settings */
    marker: '// ANSIBLE MANAGED BLOCK for Custom WP Settings'
    insertbefore: "/* That's all, stop editing! Happy publishing. */"
  changed_when: true

- name: Test Nginx configuration
  ansible.builtin.command:
    cmd: nginx -t
  become: true
  changed_when: false

- name: Set proper file permissions
  ansible.builtin.file:
    path: '{{ item.path }}'
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '{{ item.mode }}'
    recurse: '{{ item.recurse | default(false) }}'
  loop:
    - { path: '{{ site_root_dir }}/public', mode: '0755', recurse: true }
    - { path: '{{ site_root_dir }}/public/wp-content', mode: '0775' }
    - { path: '{{ site_root_dir }}/public/wp-config.php', mode: '0640' }
