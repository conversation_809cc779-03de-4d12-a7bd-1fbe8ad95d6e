---
galaxy_info:
  author: deploysite
  description: Reusable Nginx configuration role for WordPress sites
  company: deploysite
  issue_tracker_url: https://github.com/deploysite/ansible-deploy/issues
  license: MIT

  min_ansible_version: '2.9'

  platforms:
    - name: Ubuntu
      versions:
        - focal
        - jammy
    - name: Debian
      versions:
        - bullseye
        - bookworm

  galaxy_tags:
    - nginx
    - wordpress
    - web

dependencies: []
