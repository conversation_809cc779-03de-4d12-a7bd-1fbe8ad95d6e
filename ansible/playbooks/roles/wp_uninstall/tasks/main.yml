---
- name: Set fact for site_root_dir
  ansible.builtin.set_fact:
    site_root_dir: '/home/<USER>/sites/{{ domain_name }}'

- name: Check if WordPress public directory exists
  ansible.builtin.stat:
    path: '{{ site_root_dir }}/public/wp-config.php'
  register: wp_public_exists

- name: Get list of active plugins
  ansible.builtin.command:
    cmd: /usr/local/bin/wp plugin list --status=active --field=name --path={{ site_root_dir }}/public
  register: active_plugins
  changed_when: false
  when: wp_public_exists.stat.exists

- name: Deactivate all plugins
  ansible.builtin.command:
    cmd: /usr/local/bin/wp plugin deactivate --all --path={{ site_root_dir }}/public
  when:
    - wp_public_exists.stat.exists
    - active_plugins.stdout_lines | length > 0
  failed_when: false
  changed_when: true

- name: Delete all plugins
  ansible.builtin.command:
    cmd: /usr/local/bin/wp plugin delete {{ item }} --path={{ site_root_dir }}/public
  loop: '{{ active_plugins.stdout_lines }}'
  changed_when: true
  when: wp_public_exists.stat.exists

- name: Get list of installed themes
  ansible.builtin.command:
    cmd: /usr/local/bin/wp theme list --field=name --path={{ site_root_dir }}/public
  register: theme_list
  changed_when: false
  when: wp_public_exists.stat.exists

- name: Get active theme
  ansible.builtin.command:
    cmd: /usr/local/bin/wp theme list --status=active --field=name --path={{ site_root_dir }}/public
  register: active_theme
  changed_when: false
  when: wp_public_exists.stat.exists

- name: Delete all themes except active theme
  ansible.builtin.command:
    cmd: /usr/local/bin/wp theme delete {{ item }} --path={{ site_root_dir }}/public
  loop: '{{ theme_list.stdout_lines | difference([active_theme.stdout]) }}'
  changed_when: true
  failed_when: false
  when: wp_public_exists.stat.exists

- name: Delete all themes
  ansible.builtin.command:
    cmd: /usr/local/bin/wp theme delete {{ item }} --path={{ site_root_dir }}/public
  loop: '{{ theme_list.stdout_lines }}'
  changed_when: true
  failed_when: false
  when: wp_public_exists.stat.exists

- name: Remove WordPress files
  ansible.builtin.file:
    path: '{{ site_root_dir }}/public'
    state: absent

- name: Remove WordPress database
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: absent
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Remove WordPress DB user
  community.mysql.mysql_user:
    name: '{{ wp_db_user }}'
    host: localhost
    state: absent
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Remove Nginx site config
  ansible.builtin.file:
    path: '/etc/nginx/sites-available/{{ domain_name }}'
    state: absent
  become: true
  notify: Reload Nginx

- name: Remove Nginx enabled site link
  ansible.builtin.file:
    path: '/etc/nginx/sites-enabled/{{ domain_name }}'
    state: absent
  become: true
  notify: Reload Nginx

- name: Remove SSL certificate files
  ansible.builtin.file:
    path: '/etc/letsencrypt/live/{{ domain_name }}'
    state: absent
  become: true

- name: Remove site directories
  ansible.builtin.file:
    path: '{{ item }}'
    state: absent
  loop:
    - '{{ site_root_dir }}/cache'
    - '{{ site_root_dir }}/logs'
    - '{{ site_root_dir }}/backups'
    - '{{ site_root_dir }}'
